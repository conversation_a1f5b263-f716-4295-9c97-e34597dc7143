# Dockerfile
# Use a multi-stage build to build the client and server separately
FROM node:16 as builder

# Set the working directory
WORKDIR /app

# Copy the package.json and package-lock.json files to the /app directory
COPY package*.json ./

# Install the dependencies
RUN npm install

# Copy the rest of the project files to the /app directory
COPY . .

# Build the client
# Assuming there is a build script in the package.json file
# If not, you may need to adjust this command
RUN npm run build

# Use a smaller image for the final stage
FROM node:16-slim

# Set the working directory
WORKDIR /app

# Copy the built client from the builder stage
COPY --from=builder /app/build ./build

# Copy the server files from the builder stage
COPY --from=builder /app/server ./server

# Copy package.json and install dependencies for server
COPY package*.json ./
RUN npm install --production

# Expose the port the server is running on
EXPOSE 3000

# Start the server
CMD ["node", "server/index.js"]