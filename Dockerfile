# Multi-service Dockerfile for Reserve.AI Scraper Project
# Runs Node.js scrapers, Expo client, and Fastify server
FROM node:18

# Install global dependencies and PM2 for process management
RUN npm install -g @expo/cli pm2

# Install system dependencies that might be needed for Puppeteer
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    procps \
    libxss1 \
    && rm -rf /var/lib/apt/lists/*

# Install Google Chrome for Puppeteer
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Copy root package.json and install root dependencies (for scrapers)
COPY package*.json ./
RUN npm install

# Copy server package.json and install server dependencies
COPY server/package*.json ./server/
WORKDIR /app/server
RUN npm install

# Copy client package.json and install client dependencies
WORKDIR /app
COPY client/package*.json ./client/
WORKDIR /app/client
RUN npm install

# Copy all project files
WORKDIR /app
COPY . .

# Create a wrapper script for the client
RUN echo '#!/bin/bash\n\
cd /app/client\n\
exec expo start --web --port 19006 --host 0.0.0.0\n\
' > /app/start-client.sh && chmod +x /app/start-client.sh

# Create ecosystem file for PM2
RUN echo 'module.exports = {\n\
  apps: [\n\
    {\n\
      name: "server",\n\
      script: "./server/index.js",\n\
      cwd: "/app",\n\
      env: {\n\
        NODE_ENV: "production"\n\
      }\n\
    },\n\
    {\n\
      name: "client",\n\
      script: "/app/start-client.sh",\n\
      cwd: "/app",\n\
      env: {\n\
        NODE_ENV: "production",\n\
        EXPO_DEVTOOLS_LISTEN_ADDRESS: "0.0.0.0"\n\
      }\n\
    }\n\
  ]\n\
};' > /app/ecosystem.config.js

# Create a startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting Reserve.AI Scraper Services..."\n\
\n\
# Start services with PM2\n\
pm2 start /app/ecosystem.config.js\n\
\n\
echo "Services started:"\n\
echo "- Server (Fastify): http://localhost:3000"\n\
echo "- Client (Expo Web): http://localhost:19006"\n\
echo "- Scrapers available: navyaims.js, hatchgolf.js"\n\
echo ""\n\
echo "To run scrapers manually:"\n\
echo "  docker exec -it <container_name> node navyaims.js"\n\
echo "  docker exec -it <container_name> node hatchgolf.js"\n\
echo ""\n\
\n\
# Keep container running and show logs\n\
pm2 logs\n\
' > /app/start.sh && chmod +x /app/start.sh

# Expose ports
# 3000 for Fastify server
# 19006 for Expo web client
EXPOSE 3000 19006

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Start all services
CMD ["/app/start.sh"]
