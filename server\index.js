const fastify = require('fastify')({ logger: true });
const fs = require('fs').promises;
const path = require('path');

// Register CORS plugin
fastify.register(require('@fastify/cors'), {
  origin: true, // Allow all origins for development
  credentials: true,
  methods: ['GET', 'PUT', 'POST', 'DELETE', 'PATCH']
});

// Path to the db.json file
const DB_PATH = path.join(__dirname, '..', 'db.json');

// Helper function to read the database
async function readDB() {
  try {
    const data = await fs.readFile(DB_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    fastify.log.error('Error reading database:', error);
    throw new Error('Failed to read database');
  }
}

// Helper function to write to the database
async function writeDB(data) {
  try {
    await fs.writeFile(DB_PATH, JSON.stringify(data, null, 4));
  } catch (error) {
    fastify.log.error('Error writing database:', error);
    throw new Error('Failed to write database');
  }
}

// Authentication endpoint
fastify.post('/api/login', async (request, reply) => {
  try {
    const { username, password } = request.body;
    const db = await readDB();
    
    if (db.login.username === username && db.login.password === password) {
      return { success: true, message: 'Login successful' };
    } else {
      reply.code(401);
      return { success: false, message: 'Invalid credentials' };
    }
  } catch (error) {
    reply.code(500);
    return { success: false, message: 'Server error' };
  }
});

// Get all apps
fastify.get('/api/apps', async (request, reply) => {
  try {
    const db = await readDB();
    return { success: true, apps: db.apps };
  } catch (error) {
    reply.code(500);
    return { success: false, message: 'Failed to fetch apps' };
  }
});

// Get specific app
fastify.get('/api/apps/:appName', async (request, reply) => {
  try {
    const { appName } = request.params;
    const db = await readDB();
    
    if (db.apps[appName]) {
      return { success: true, app: db.apps[appName] };
    } else {
      reply.code(404);
      return { success: false, message: 'App not found' };
    }
  } catch (error) {
    reply.code(500);
    return { success: false, message: 'Failed to fetch app' };
  }
});

// Update app status
fastify.patch('/api/apps/:appName/status', async (request, reply) => {
  try {
    const { appName } = request.params;
    const { status } = request.body;
    
    if (!['on', 'off'].includes(status)) {
      reply.code(400);
      return { success: false, message: 'Status must be "on" or "off"' };
    }
    
    const db = await readDB();
    
    if (!db.apps[appName]) {
      reply.code(404);
      return { success: false, message: 'App not found' };
    }
    
    db.apps[appName].status = status;
    await writeDB(db);
    
    return { success: true, message: 'Status updated successfully', app: db.apps[appName] };
  } catch (error) {
    reply.code(500);
    return { success: false, message: 'Failed to update status' };
  }
});

// Update entire app
fastify.put('/api/apps/:appName', async (request, reply) => {
  try {
    const { appName } = request.params;
    const appData = request.body;

    const db = await readDB();

    if (!db.apps[appName]) {
      reply.code(404);
      return { success: false, message: 'App not found' };
    }

    // Validate and clean the data
    const cleanedData = {};
    for (const [key, value] of Object.entries(appData)) {
      if (key === 'variants' && typeof value === 'object' && value !== null) {
        // Handle variants object
        cleanedData[key] = value;
      } else if (typeof value === 'string' || typeof value === 'boolean') {
        // Handle simple properties
        cleanedData[key] = value;
      }
    }

    db.apps[appName] = { ...db.apps[appName], ...cleanedData };
    await writeDB(db);

    fastify.log.info(`App ${appName} updated:`, db.apps[appName]);

    return { success: true, message: 'App updated successfully', app: db.apps[appName] };
  } catch (error) {
    fastify.log.error('Error updating app:', error);
    reply.code(500);
    return { success: false, message: 'Failed to update app' };
  }
});

// Health check endpoint
fastify.get('/api/health', async () => {
  return { status: 'OK', timestamp: new Date().toISOString() };
});

// Start the server
const start = async () => {
  try {
    await fastify.listen({ port: 3000, host: '0.0.0.0' });
    fastify.log.info('Server is running on http://localhost:3000');
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
