"use strict";

exports.__esModule = true;
exports.default = void 0;
/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

var I18nManager = {
  allowRTL() {
    return;
  },
  forceRTL() {
    return;
  },
  getConstants() {
    return {
      isRTL: false
    };
  }
};
var _default = exports.default = I18nManager;
module.exports = exports.default;