version: '3.8'

services:
  reserveai:
    build: .
    container_name: reserve-ai-scraper
    ports:
      - "3000:3000"    # Fastify server
      - "19006:19006"  # Expo web client
    volumes:
      - ./db.json:/app/db.json  # Mount database file for persistence
      - ./ChromeProfile:/app/ChromeProfile  # Mount Chrome profiles if needed
      - ./ChromeProfile2:/app/ChromeProfile2
      - ./ChromeProfile3:/app/ChromeProfile3
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
    restart: unless-stopped
    # Add security options for Chrome
    security_opt:
      - seccomp:unconfined
    # Add shared memory size for Chrome
    shm_size: 2gb
