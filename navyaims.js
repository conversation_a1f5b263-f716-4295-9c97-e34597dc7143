const moment = require('moment/moment');
const fs = require('fs');

const delay = (time) => {
    return new Promise(function (resolve) {
        setTimeout(resolve, time)
    });
}

function delayUntil(time) {
    const ms = new Date(time).getTime() - new Date().getTime();
    console.log('Wait for ' + ms / 1000 + 's')
    return new Promise(function (resolve) {
        setTimeout(resolve, ms)
    });
}

const login = async (page, user, pass) => {
    await page.goto('https://myffr.navyaims.com/navywest/wbwsc/navywest.wsc/login.html', { timeout: 120000 })
    await delay(5000);

    await page.waitForSelector('input#weblogin_username');
    const inputUser = await page.$('input#weblogin_username');
    await inputUser.focus()
    await page.keyboard.type(user, { delay: 100 })

    const inputPass = await page.$('input#weblogin_password');
    await inputPass.focus()
    await page.keyboard.type(pass, { delay: 100 })
    await page.click('button#weblogin_buttonlogin')

    // if multi session detected
    try {
        await page.click('#loginresumesession_buttoncontinue', { timeout: 3000 })
    } catch (ex) { }
}

const compareHour = (t1, t2) => {
    return new Date('2000-01-01 ' + t1).getTime() - new Date('2000-01-01 ' + t2).getTime()
}

const start = async () => {
    try {

        let db = { status: 'on'}
        try {
            db = JSON.parse(fs.readFileSync('db.json', 'utf8'));
            db = db.apps.navyaims
        } catch (ex) { }

        if (!db.status || db.status === 'off') {
            console.log('Navy AIMS is off');
            return;
        }

        const orderDate = db.orderDate;
        const orderTime = '13:00';

        const [hours, minutes] = orderTime.split(':');

        // Create a Date object in CST
        let targetDate = new Date(orderDate);
        targetDate.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);

        // Adjust to CST (UTC-6 during standard time, UTC-5 during daylight saving time)
        // const cstOffset = -5; // Use -6 for standard time
        const now = new Date();
        const diffMs = targetDate - now;
        const diffHours = diffMs / (1000 * 60 * 60);
        if (diffHours > 1) {
            console.log('Order time is more than 1 hour away. Skipping... ' + diffHours);
            if (browser) await browser.close();
            return;
        }

        console.log(new Date())
        var { connect } = await import('puppeteer-real-browser')
        const { page, browser } = await connect({
            headless: false,
            defaultViewport: null,
            args: ['--start-maximized'],
            customConfig: {
                // userDataDir: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data'
                userDataDir: 'C:\\Bagide\\OFE\\reserve.ai\\scraper\\ChromeProfile',
            },
            connectOption: {
                defaultViewport: null,
            },
            // userDataDir: `${process.env.LOCALAPPDATA}\\Google\\Chrome\\User Data\\Default`,
            executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            skipTarget: [],
            fingerprint: true,
            turnstile: true,
            tf: true,

        })
        await login(page, 'johnhahn1', 'Lagolf0809!')
        await delay(2000);

        await page.waitForSelector('li[role="menuitem"]')
        let menus = await page.$$('li[role="menuitem"]')
        await menus[1].click()
        menus = await page.$$('a.menuitem')
        await menus[6].click()
        await page.waitForNavigation({ waitUntil: 'networkidle2' });
        // await delay(3000);

        const request = {
            date: db.orderDate ? moment(db.orderDate).format('MM/DD/YYYY') : moment().add(16, 'day').format('MM/DD/YYYY'), // '09/06/2024',
            timeStart: db.timeStart || '9:00 AM',
            timeEnd: db.timeEnd || '10:30 AM',
        }

        await page.waitForSelector('select[name="secondarycode"]')
        console.log(request.date)
        await page.evaluate((request) => {
            document.querySelector('select[name="secondarycode"]').value = 25
            document.querySelector('input[name="begindate"]').value = request.date
            document.querySelector('input[name="begintime"]').value = request.timeStart
        }, request)

        await delayUntil(new Date().setHours(20, 0, 0, 0))
        await page.click('button#grwebsearch_buttonsearch')
        await delay(5000);
        const carts = await page.$$('#grwebsearch_output_table a.button.success.cart-button')
        if (carts.length) {
            const ticket = await page.evaluate(() => {
                const row = document.querySelector('#grwebsearch_output_table tbody tr').childNodes
                return {
                    time: row[2].textContent,
                }
            })
            if (compareHour(request.timeStart, ticket.time) <= 0 && compareHour(request.timeEnd, ticket.time) >= 0) {
                console.log('Trying to reserve ' + ticket.time)
                await carts[0].click()
                await delay(5000)
                await page.click('button#golfmemberselection_buttononeclicktofinish')
            } else {
                console.log('No available time slot ' + ticket.time)
            }
        } else {
            console.log('No available slot')
        }

        console.log('Done!')
        // await delay(10000)
        // browser.close()
    } catch (err) {
        console.error(err)
        process.exit(1)
    }
}
start()