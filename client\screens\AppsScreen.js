import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  RefreshControl,
  ActivityIndicator,
  TextInput,
} from 'react-native';

const AppsScreen = ({ navigation }) => {
  const [apps, setApps] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [expandedApps, setExpandedApps] = useState({});
  const [editingApps, setEditingApps] = useState({});
  const [editedData, setEditedData] = useState({});

  const fetchApps = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/apps');
      const data = await response.json();

      if (data.success) {
        setApps(data.apps);
      } else {
        Alert.alert('Error', 'Failed to fetch apps');
      }
    } catch (error) {
      console.error('Fetch error:', error);
      Alert.alert('Connection Error', 'Unable to connect to server');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const toggleAppStatus = async (appName, currentStatus) => {
    const newStatus = currentStatus === 'on' ? 'off' : 'on';

    try {
      const response = await fetch(`http://localhost:3000/api/apps/${appName}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (data.success) {
        setApps(prev => ({
          ...prev,
          [appName]: { ...prev[appName], status: newStatus }
        }));

        // Auto-expand when turning on, collapse when turning off
        setExpandedApps(prev => ({
          ...prev,
          [appName]: newStatus === 'on'
        }));
      } else {
        Alert.alert('Error', data.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Update error:', error);
      Alert.alert('Connection Error', 'Unable to connect to server');
    }
  };

  const toggleExpanded = (appName) => {
    setExpandedApps(prev => ({
      ...prev,
      [appName]: !prev[appName]
    }));
  };

  const startEditing = (appName) => {
    setEditingApps(prev => ({ ...prev, [appName]: true }));
    setEditedData(prev => ({ ...prev, [appName]: { ...apps[appName] } }));
  };

  const cancelEditing = (appName) => {
    setEditingApps(prev => ({ ...prev, [appName]: false }));
    setEditedData(prev => {
      const newData = { ...prev };
      delete newData[appName];
      return newData;
    });
  };

  const saveChanges = async (appName) => {
    try {
      const response = await fetch(`http://localhost:3000/api/apps/${appName}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editedData[appName]),
      });

      const data = await response.json();

      if (data.success) {
        setApps(prev => ({
          ...prev,
          [appName]: data.app
        }));
        setEditingApps(prev => ({ ...prev, [appName]: false }));
        setEditedData(prev => {
          const newData = { ...prev };
          delete newData[appName];
          return newData;
        });
        Alert.alert('Success', 'App updated successfully');
      } else {
        Alert.alert('Error', data.message || 'Failed to update app');
      }
    } catch (error) {
      console.error('Update error:', error);
      Alert.alert('Connection Error', 'Unable to connect to server');
    }
  };

  const updateEditedField = (appName, field, value) => {
    setEditedData(prev => ({
      ...prev,
      [appName]: {
        ...prev[appName],
        [field]: value
      }
    }));
  };

  const updateEditedVariant = (appName, variantName, value) => {
    setEditedData(prev => ({
      ...prev,
      [appName]: {
        ...prev[appName],
        variants: {
          ...prev[appName].variants,
          [variantName]: value
        }
      }
    }));
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchApps();
  };

  const logout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', onPress: () => navigation.replace('Login') }
      ]
    );
  };

  useEffect(() => {
    fetchApps();
  }, []);

  const renderAppProperty = (appName, key, value, isEditing) => {
    if (key === 'status') return null; // Status is handled separately

    if (typeof value === 'object' && value !== null) {
      // Handle variants with checkboxes
      if (key === 'variants') {
        return (
          <View key={key} style={styles.propertyContainer}>
            <Text style={styles.propertyLabel}>{key}:</Text>
            {Object.entries(value).map(([subKey, subValue]) => (
              <View key={subKey} style={styles.variantContainer}>
                <Text style={styles.variantLabel}>{subKey}:</Text>
                {isEditing ? (
                  <Switch
                    value={editedData[appName]?.variants?.[subKey] ?? subValue}
                    onValueChange={(newValue) => updateEditedVariant(appName, subKey, newValue)}
                    trackColor={{ false: '#767577', true: '#81b0ff' }}
                    thumbColor={editedData[appName]?.variants?.[subKey] ?? subValue ? '#007AFF' : '#f4f3f4'}
                  />
                ) : (
                  <Text style={styles.variantValue}>{String(subValue)}</Text>
                )}
              </View>
            ))}
          </View>
        );
      }

      // Handle other objects (non-editable for now)
      return (
        <View key={key} style={styles.propertyContainer}>
          <Text style={styles.propertyLabel}>{key}:</Text>
          {Object.entries(value).map(([subKey, subValue]) => (
            <View key={subKey} style={styles.subPropertyContainer}>
              <Text style={styles.subPropertyLabel}>{subKey}:</Text>
              <Text style={styles.subPropertyValue}>{String(subValue)}</Text>
            </View>
          ))}
        </View>
      );
    }

    // Handle simple properties with text inputs
    return (
      <View key={key} style={styles.propertyContainer}>
        <Text style={styles.propertyLabel}>{key}:</Text>
        {isEditing ? (
          <TextInput
            style={styles.editInput}
            value={editedData[appName]?.[key] ?? String(value)}
            onChangeText={(text) => updateEditedField(appName, key, text)}
            placeholder={`Enter ${key}`}
          />
        ) : (
          <Text style={styles.propertyValue}>{String(value)}</Text>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading apps...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Reserve AI</Text>
        <TouchableOpacity onPress={logout} style={styles.logoutButton}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {Object.entries(apps).map(([appName, appData]) => {
          const isOn = appData.status === 'on';
          const isExpanded = expandedApps[appName] || isOn;
          const isEditing = editingApps[appName];

          return (
            <View key={appName} style={[styles.card, isOn && styles.cardActive]}>
              <TouchableOpacity
                style={styles.cardHeader}
                onPress={() => toggleExpanded(appName)}
              >
                <View style={styles.cardHeaderLeft}>
                  <Text style={[styles.appName, isOn && styles.appNameActive]}>
                    {appName}
                  </Text>
                  <Text style={[styles.statusText, isOn && styles.statusTextActive]}>
                    Status: {appData.status}
                  </Text>
                </View>
                <Switch
                  value={isOn}
                  onValueChange={() => toggleAppStatus(appName, appData.status)}
                  trackColor={{ false: '#767577', true: '#81b0ff' }}
                  thumbColor={isOn ? '#007AFF' : '#f4f3f4'}
                />
              </TouchableOpacity>

              {isExpanded && (
                <View style={styles.cardContent}>
                  <View style={styles.divider} />

                  {/* Edit/Save/Cancel buttons */}
                  <View style={styles.actionButtons}>
                    {isEditing ? (
                      <>
                        <TouchableOpacity
                          style={[styles.actionButton, styles.saveButton]}
                          onPress={() => saveChanges(appName)}
                        >
                          <Text style={styles.saveButtonText}>Save</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.actionButton, styles.cancelButton]}
                          onPress={() => cancelEditing(appName)}
                        >
                          <Text style={styles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[styles.actionButton, styles.editButton]}
                        onPress={() => startEditing(appName)}
                      >
                        <Text style={styles.editButtonText}>Edit</Text>
                      </TouchableOpacity>
                    )}
                  </View>

                  {Object.entries(appData).map(([key, value]) =>
                    renderAppProperty(appName, key, value, isEditing)
                  )}
                </View>
              )}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50, // Account for status bar
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  logoutButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#ff3b30',
    borderRadius: 6,
  },
  logoutText: {
    color: '#fff',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardActive: {
    borderLeftWidth: 4,
    borderLeftColor: '#34c759',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  cardHeaderLeft: {
    flex: 1,
  },
  appName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  appNameActive: {
    color: '#34c759',
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  statusTextActive: {
    color: '#34c759',
    fontWeight: '600',
  },
  cardContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginBottom: 15,
  },
  propertyContainer: {
    marginBottom: 12,
  },
  propertyLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  propertyValue: {
    fontSize: 16,
    color: '#666',
  },
  subPropertyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 2,
  },
  subPropertyLabel: {
    fontSize: 14,
    color: '#666',
  },
  subPropertyValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 15,
    gap: 10,
  },
  actionButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: '#007AFF',
  },
  saveButton: {
    backgroundColor: '#34c759',
  },
  cancelButton: {
    backgroundColor: '#ff3b30',
  },
  editButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  editInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 8,
    fontSize: 16,
    backgroundColor: '#fff',
    marginTop: 4,
  },
  variantContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 8,
    backgroundColor: '#f8f9fa',
    marginVertical: 2,
    borderRadius: 6,
  },
  variantLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  variantValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});

export default AppsScreen;
