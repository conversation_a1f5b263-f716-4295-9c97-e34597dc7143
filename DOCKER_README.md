# Reserve.AI Scraper Docker Setup

This Docker setup runs all three components of the Reserve.AI Scraper project in a single container:

1. **Node.js Scrapers** - `navyaims.js` and `hatchgolf.js`
2. **Expo Client** - React Native web client (port 19006)
3. **Fastify Server** - API server (port 3000)

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d --build

# Stop services
docker-compose down
```

### Using Docker directly

```bash
# Build the image
docker build -t reserve-ai-scraper .

# Run the container
docker run -p 3000:3000 -p 19006:19006 \
  -v $(pwd)/db.json:/app/db.json \
  --name reserve-ai-scraper \
  reserve-ai-scraper
```

## Accessing Services

Once the container is running, you can access:

- **Fastify Server**: http://localhost:3000
- **Expo Web Client**: http://localhost:19006
- **Server API**: http://localhost:3000/api (check server routes)

## Running Scrapers

The scrapers (`navyaims.js` and `hatchgolf.js`) are available in the container but don't run automatically. To execute them:

```bash
# Run navyaims scraper
docker exec -it reserve-ai-scraper node navyaims.js

# Run hatchgolf scraper
docker exec -it reserve-ai-scraper node hatchgolf.js

# Access container shell
docker exec -it reserve-ai-scraper bash
```

## Environment Variables

The following environment variables are set in the container:

- `NODE_ENV=production`
- `PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true`
- `PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable`

## Volumes

The docker-compose setup mounts:

- `./db.json` - Database file for data persistence
- Chrome profiles (if needed for scraping)

## Process Management

The container uses PM2 to manage processes:

```bash
# View running processes
docker exec -it reserve-ai-scraper pm2 list

# View logs
docker exec -it reserve-ai-scraper pm2 logs

# Restart a service
docker exec -it reserve-ai-scraper pm2 restart server
docker exec -it reserve-ai-scraper pm2 restart client
```

## Troubleshooting

### Chrome/Puppeteer Issues

If you encounter Chrome-related errors:

1. The container includes Google Chrome and necessary dependencies
2. Chrome runs with `--no-sandbox` flag for Docker compatibility
3. Shared memory is set to 2GB for Chrome stability

### Port Conflicts

If ports 3000 or 19006 are already in use:

```bash
# Change ports in docker-compose.yml
ports:
  - "3001:3000"    # Use port 3001 instead of 3000
  - "19007:19006"  # Use port 19007 instead of 19006
```

### Memory Issues

For large scraping operations, you may need to increase container memory:

```bash
docker run --memory=4g --memory-swap=4g ...
```

## Development

For development with live reloading, you can mount the source code:

```bash
docker run -p 3000:3000 -p 19006:19006 \
  -v $(pwd):/app \
  -v /app/node_modules \
  -v /app/client/node_modules \
  -v /app/server/node_modules \
  reserve-ai-scraper
```
