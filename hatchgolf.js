const TARGET_URL = 'https://www.hatchgolf.com/collections/new-releases';
// const TARGET_URL = 'https://www.hatchgolf.com/collections/shop-all';

const delay = (time) => {
    return new Promise(function (resolve) {
        setTimeout(resolve, time)
    });
}

const getNewProduct = async (page, searchTitle) => {
    console.log(`Navigating to ${TARGET_URL}`);
    await page.goto(TARGET_URL, { waitUntil: 'networkidle2' });

    // Extract the first product
    const product = await page.evaluate((searchTitle) => {
        // const firstProduct = document.querySelector('.ProductItem'); // Adjust selector as needed
        let firstProduct = null
        const allProducts = document.querySelectorAll('.ProductItem .ProductItem__Title a'); // Adjust selector as needed
        console.log('allProducts', allProducts);
        const filtered = [...allProducts].filter(product => {
            if (product.innerText.includes(searchTitle)) {
                return true
            }
            return false;
        });

        if (!filtered.length) {
            return null
        } else if (filtered.length === 1) {
            firstProduct = filtered[0].parentElement.parentElement.parentElement.parentElement;
        } else {
            const filtered2 = filtered.filter(product => {
                if (product.innerText.includes('DRIVER')) {
                    return true
                }
                return false;
            });
            if (filtered2.length === 1) {
                firstProduct = filtered2[0].parentElement.parentElement.parentElement.parentElement;
            } else {
                firstProduct = filtered[0].parentElement.parentElement.parentElement.parentElement;
            }
        }
        if (!firstProduct) {
            return null;
        }

        const titleElement = firstProduct.querySelector('.ProductItem__Title'); // Adjust selector
        const priceElement = firstProduct.querySelector('.ProductItem__Price'); // Adjust selector
        const linkElement = firstProduct.querySelector('a'); // Adjust selector for the product link
        const availabilityElement = firstProduct.querySelector('.ProductItem__Label--soldOut'); // Adjust selector for sold out badge

        const title = titleElement ? titleElement.innerText.trim() : 'N/A';
        const price = priceElement ? priceElement.innerText.trim() : 'N/A';
        const link = linkElement ? linkElement.href : 'N/A';
        const isAvailable = !availabilityElement; // If sold out badge exists, it's not available

        return { title, price, link, isAvailable };
    }, searchTitle);
    return product;
}

const getProductFromPage = async (page, url) => {
    await page.goto(url, { waitUntil: 'networkidle2' });
    const product = await page.evaluate(() => {
        const titleElement = document.querySelector('.ProductMeta__Title'); // Adjust selector
        const priceElement = document.querySelector('.ProductMeta__Price'); // Adjust selector

        const title = titleElement ? titleElement.innerText.trim() : 'N/A';
        const price = priceElement ? priceElement.innerText.trim() : 'N/A';
        const isAvailable = true;

        return { title, price, isAvailable };
    });

    return { ...product, link: url };
}

const addVariantToCart = async (browser, url, variant) => {
    try {
        const page = await browser.newPage();

        const session = await page.target().createCDPSession();
        await session.send("Page.enable");
        await session.send("Page.setWebLifecycleState", { state: "active" });
        await session.send(`Emulation.setFocusEmulationEnabled`, { enabled: true });

        await page.goto(url, { waitUntil: 'networkidle2' });
        console.log(`Attempting to select variant: ${variant}`);
        // Extract the first product
        const t = await page.evaluate(async (variant) => {
            const delay = (time) => {
                return new Promise(function (resolve) {
                    setTimeout(resolve, time)
                });
            }

            document.querySelector('input[name="quantity"]').value = 3;
            document.querySelector('button.ProductForm__Item').click();
            do {
                await delay(500); // Wait for the variant selector to open
            } while (!document.querySelector(`button[data-value${variant}]`));
            document.querySelector(`button[data-value${variant}]`).click();
            document.querySelector('button[data-action="add-to-cart"]').click();

            return;
        }, variant);
        await page.waitForNavigation({ waitUntil: 'networkidle2' });
        page.close();
    } catch (error) {
        console.error(`Error adding variant "${variant}" to cart:`, error);
    }
    return true;
}

const addToCart = async (browser, url, variants) => {
    try {
        const page = await browser.newPage();

        const session = await page.target().createCDPSession();
        await session.send("Page.enable");
        await session.send("Page.setWebLifecycleState", { state: "active" });
        await session.send(`Emulation.setFocusEmulationEnabled`, { enabled: true });

        await page.goto(url, { waitUntil: 'networkidle2' });
        console.log(`Get variants...`);
        // Extract the first product
        const t = await page.evaluate(async (variants) => {
            const productId = document.querySelector('input[name="product-id"]').value;
            const opts = document.querySelector('select[title="Variant"]').options;
            let proms = []
            for (const opt of opts) {
                for (const variant of variants) {
                    console.log(`Checking variant ${opt.text} for ${variant}`);
                    if (opt.text.includes(variant)) {
                        console.log(`Found variant ${opt.text}: ${opt.value}`);

                        let formData = new FormData();

                        formData.append('form_type', 'product')
                        formData.append('utf8', '✓')
                        formData.append('id', opt.value)
                        formData.append('quantity', '3')
                        formData.append('product-id', productId)
                        formData.append('section-id', 'product-template')

                        proms.push(fetch("https://www.hatchgolf.com/cart/add", {
                            method: 'POST',
                            body: formData
                        }))
                    }
                }
            }

            await Promise.all(proms);
            return;
        }, variants);
        // await page.waitForNavigation({ waitUntil: 'networkidle2' });
        page.close();
    } catch (error) {
        console.error(`Error adding to cart:`, error);
    }
    return true;
}

function delayUntil(time) {
    const ms = new Date(time).getTime() - new Date().getTime();
    console.log('Wait until: ' + time + ' (' + ms / 1000 + 's)')
    return new Promise(function (resolve) {
        setTimeout(resolve, ms)
    });
}

async function scrapeAndBuy() {
    const idx = process.argv[2];
    var { connect } = await import('puppeteer-real-browser');
    const profiles = ['ChromeProfile', 'ChromeProfile2', 'ChromeProfile3'];
    const { page, browser } = await connect({
        headless: false,
        defaultViewport: null,
        args: ['--start-maximized', '--disable-features=site-per-process'],
        customConfig: {
            userDataDir: `C:\\Bagide\\OFE\\reserve.ai\\scraper\\${profiles[idx]}`,
        },
        connectOption: { defaultViewport: null },
        executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
    });
    try {
        let db = {}
        try {
            db = JSON.parse(fs.readFileSync('db.json', 'utf8'));
            db = db.apps.hatchgolf
        } catch (ex) { }

        if (!db.status || db.status === 'off') {
            console.log('Hatchgolf is off');
            return;
        }

        const orderDate = db.orderDate;
        const orderTime = db.orderTime;

        const [hours, minutes] = orderTime.split(':');

        // Create a Date object in CST
        let targetDate = new Date(orderDate);
        targetDate.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);

        // Adjust to CST (UTC-6 during standard time, UTC-5 during daylight saving time)
        const cstOffset = -5; // Use -6 for standard time
        targetDate.setHours(targetDate.getHours() + cstOffset);

        console.log(targetDate);
        const now = new Date();
        const diffMs = targetDate - now;
        const diffHours = diffMs / (1000 * 60 * 60);
        if (diffHours > 1) {
            console.log('Order time is more than 1 hour away. Skipping...');
            if (browser) await browser.close();
            return;
        }
        await delayUntil(targetDate);

        console.log('Started at: ', new Date());
        let retryCount = 0;
        let product = null
        const productName = db.itemName
        do {
            product = await getNewProduct(page, productName);
            await delay(1000);
        } while (!product && retryCount++ < 10);
        //await page.setViewport({ width: 1200, height: 800 });
        // page2.goto(TARGET_URL, { waitUntil: 'networkidle2' });
        // page3.goto(TARGET_URL, { waitUntil: 'networkidle2' });

        // const product = await getProductFromPage(page, 'https://www.hatchgolf.com/collections/shop-all/products/5-25-electric-avenue');
        // const variants = ['="FW Wood"', '="Driver"', '="Hybrid"'];
        const variants = ['FW Wood', 'Driver', 'Hybrid'];
        if (product) {
            console.log('Product Found:', product);

            if (product.isAvailable) {
                console.log(`Product "${product.title}" is available! Attempting to buy...`);
                // Placeholder for buying logic
                console.log(`Navigating to product page: ${product.link}`);

                await addToCart(browser, product.link, [variants[idx]]);

                // const awaitVariants = []
                // for (const variant of variants) {
                //     awaitVariants.push(addVariantToCart(browser, product.link, variant));
                //     await delay(1000);
                // }

                // await Promise.all(awaitVariants);

                console.log('All variants added to cart. Attempting to proceed to checkout...');
                await page.click('a[href="/cart"]');
                // await page.waitForNavigation({ waitUntil: 'networkidle2' }); // Wait for navigation after adding to cart
                await delay(2000); // Wait for the variant selector to open

                // Attempt to click a "Checkout" button or navigate to checkout page
                const checkoutButtonSelector = 'button[name="checkout"]'; // Common selector for Shopify checkout link
                console.log(`Attempting to click "Checkout" button with selector: ${checkoutButtonSelector}`);
                await page.click(checkoutButtonSelector);
                await page.waitForNavigation({ waitUntil: 'networkidle2' }); // Wait for navigation to checkout

                try {
                    await delay(2000); // Wait for the variant selector to open
                    const payButtonSelector = 'button[aria-label="Pay now"]'; // Common selector for Shopify checkout link
                    console.log(`Attempting to click "Pay now" button with selector: ${payButtonSelector}`);
                    await page.click(payButtonSelector);
                    await page.waitForNavigation({ waitUntil: 'networkidle2' }); // Wait for navigation to checkout
                } catch (error) {
                    console.error('Error clicking "Pay now" button:', error);
                    console.log('Manual intervention may be required to complete the purchase.');
                    await delay(30000);
                }


            } else {
                console.log(`Product "${product.title}" is not available (Sold Out).`);
                if (browser) await browser.close();
            }
        } else {
            console.log('No products found on the page.');
            if (browser) await browser.close();
        }

    } catch (error) {
        console.error('An error occurred:', error);
        if (browser) await browser.close();
    } finally {
        console.log('Ended at: ', new Date());
        // if (browser) {
        //     await browser.close();
        // }
    }
}

scrapeAndBuy();